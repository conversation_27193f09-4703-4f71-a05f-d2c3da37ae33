import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from './api-base';
import * as models from './models/boekestyns';

export const boekestynStickingApi = createApi({
  reducerPath: 'boekestyn-sticking-api',
  baseQuery: axiosBaseQuery('boekestyns/sticking/'),
  refetchOnMountOrArgChange: true,
  tagTypes: ['StickingSchedules', 'StickingWorkOrdersByDate'],
  endpoints: (builder) => ({
    sticking: builder.query<BoekestynStickingResponse, void>({
      query: () => ({
        url: '',
      }),
    }),
    stickingOrders: builder.query<
      BoekestynStickingOrdersResponse,
      BoekestynStickingOrdersArgs
    >({
      query: ({ startDate, endDate }) => ({
        url: `orders?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    stickingSchedules: builder.query<
      BoekestynStickingSchedulesResponse,
      BoekestynStickingSchedulesArgs
    >({
      query: ({ date }) => ({
        url: `schedules?date=${date}`,
      }),
      providesTags: ['StickingSchedules'],
    }),
    addStickingOrderToStickingSchedule: builder.mutation<
      void,
      AddStickingOrderToStickingScheduleArgs
    >({
      query: (data) => ({
        url: `workOrders`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['StickingSchedules'],
    }),
    sortStickingWorkOrders: builder.mutation<void, SortStickingWorkOrdersArgs>({
      query: (data) => ({
        url: `workOrders/sort`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['StickingSchedules'],
    }),
    updateStickingWorkOrderComment: builder.mutation<
      void,
      UpdateStickingWorkOrderCommentArgs
    >({
      query: ({ id, comment }) => ({
        url: `workOrders/${id}/comment`,
        method: 'POST',
        data: { comment },
      }),
      invalidatesTags: ['StickingSchedules'],
    }),
    updateStickingWorkOrderCrewSize: builder.mutation<
      void,
      UpdateStickingWorkOrderCrewSizeArgs
    >({
      query: ({ id, crewSize }) => ({
        url: `workOrders/${id}/crewSize`,
        method: 'POST',
        data: { crewSize },
      }),
      invalidatesTags: ['StickingSchedules'],
    }),
    deleteStickingWorkOrder: builder.mutation<
      void,
      DeleteStickingWorkOrderArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.id}/remove`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['StickingSchedules'],
    }),
    getStickingWorkOrdersByDate: builder.query<
      StickingWorkOrdersByDateResponse,
      string
    >({
      query: (date) => ({
        url: `workOrders?date=${date}`,
      }),
      providesTags: ['StickingWorkOrdersByDate'],
    }),
    startStickingWorkOrderLabour: builder.mutation<
      void,
      StartStickingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/start`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['StickingWorkOrdersByDate'],
    }),
    pauseStickingWorkOrderLabour: builder.mutation<
      void,
      PauseStickingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/pause`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['StickingWorkOrdersByDate'],
    }),
    stopStickingWorkOrderLabour: builder.mutation<
      void,
      StopStickingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/stop`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['StickingWorkOrdersByDate'],
    }),
  }),
});

export interface BoekestynStickingResponse {
  lines: models.StickingLine[];
}

interface BoekestynStickingOrdersArgs {
  startDate: string;
  endDate: string;
}

export interface BoekestynStickingOrdersResponse {
  orders: models.StickingOrder[];
}

interface BoekestynStickingSchedulesArgs {
  date: string;
}

export interface BoekestynStickingSchedulesResponse {
  schedules: models.StickingSchedule[];
}

export interface AddStickingOrderToStickingScheduleArgs {
  schedule: models.StickingSchedule;
  order: models.StickingOrder;
}

export interface DeleteStickingWorkOrderArgs {
  id: number;
  orderId: string;
}

export interface SortStickingWorkOrdersArgs {
  workOrders: { workOrderId: number; sortOrder: number }[];
}

export interface UpdateStickingWorkOrderCommentArgs {
  id: number;
  comment: string | null;
}

export interface UpdateStickingWorkOrderCrewSizeArgs {
  id: number;
  crewSize: number;
}

export interface StickingWorkOrdersByDateResponse {
  orders: models.StickingWorkOrderItem[];
}

export interface StartStickingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
}

export interface PauseStickingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
  comments: string | null;
}

export interface StopStickingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
  comments: string | null;
}

export const {
  useStickingQuery,
  useStickingOrdersQuery,
  useStickingSchedulesQuery,
  useAddStickingOrderToStickingScheduleMutation,
  useSortStickingWorkOrdersMutation,
  useDeleteStickingWorkOrderMutation,
  useUpdateStickingWorkOrderCommentMutation,
  useUpdateStickingWorkOrderCrewSizeMutation,
  useGetStickingWorkOrdersByDateQuery,
  useStartStickingWorkOrderLabourMutation,
  usePauseStickingWorkOrderLabourMutation,
  useStopStickingWorkOrderLabourMutation,
} = boekestynStickingApi;
