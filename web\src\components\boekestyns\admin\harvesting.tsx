export function Harvesting() {
  return (
    <div className="flex h-full flex-col overflow-y-auto bg-blue-500">
      <div className="m-4 flex-grow overflow-y-auto rounded bg-white">
        <div>
          <h1 className="p-3 text-center text-2xl font-bold">Harvesting</h1>
        </div>
        <div className="flex-grow overflow-y-auto">
          <div className="grid h-full grid-cols-2 overflow-y-auto">
            <div className="flex flex-col overflow-y-auto">
              <div className="flex flex-col overflow-y-auto">
                <div className="p-4">
                  <div>
                    <label htmlFor="start-date">From</label>
                    <input min="1" max="53" value="14" className="text-xs" />
                  </div>
                </div>
                <div className="flex-grow overflow-y-auto">
                  <div className="h-screen bg-green-50">&nbsp;</div>
                </div>
              </div>
            </div>
            <div className="h-full overflow-y-auto">
              <div className="h-screen bg-yellow-50">&nbsp;</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
