import React, { Fragment, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import * as HeadlessUI from '@headlessui/react';
import { useSalesQuery } from 'api/boekestyn-service';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import {
  clearError,
  clearSearchAndFilters,
  downloadSales,
  setStartDate,
  setEndDate,
  selectStartDate,
  selectEndDate,
  setFilterPlant,
  selectFilterPlant,
  setProductionCustomers,
  selectProductionCustomers,
  selectIsLoading,
  selectError,
  selectPlants,
  selectSalesCustomers,
  selectBloomingAssortedSalesCustomers,
  selectWeeks,
  selectCustomers,
  selectMultipleProductItems,
} from '@/components/boekestyns/sales/sales-slice';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { CustomerItem } from '@/components/boekestyns/sales/customer-item';
import { ProductionItem } from '@/components/boekestyns/sales/production-item';
import { SalesItemSingle } from '@/components/boekestyns/sales/sales-item-single';
import { SalesItemMultiple } from '@/components/boekestyns/sales/sales-item-multiple';
import { AvailableItem } from '@/components/boekestyns/sales/available-item';
import { classNames } from '@/utils/class-names';

export default function Sales() {
  const dispatch = useAppDispatch(),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    plants = useAppSelector(selectPlants),
    salesCustomers = useAppSelector(selectSalesCustomers),
    bloomingAssortedSalesCustomers = useAppSelector(
      selectBloomingAssortedSalesCustomers
    ),
    customers = useAppSelector(selectCustomers),
    weeks = useAppSelector(selectWeeks),
    filterPlant = useAppSelector(selectFilterPlant),
    productionCustomers = useAppSelector(selectProductionCustomers),
    multipleProductItems = useAppSelector(selectMultipleProductItems),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    { refetch } = useSalesQuery({ startDate, endDate }),
    [showMultipleCustomers, setShowMultipleCustomers] = useState(false),
    hasMultipleProductItems = multipleProductItems.length > 0;

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setStartDate(e.target.value));
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setEndDate(e.target.value));
  };

  const handleFilterPlantChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(setFilterPlant(e.target.value));
  };

  const handleProductionCustomerChange = (customers: string[]) => {
    dispatch(setProductionCustomers(customers));
  };

  const handleToggleMultipleCustomersClick = () => {
    setShowMultipleCustomers(!showMultipleCustomers);
  };

  const handleResetSearchClick = async () => {
    await dispatch(clearSearchAndFilters());
    window.setTimeout(refetch);
  };

  const handleDownloadClick = () => {
    dispatch(downloadSales());
  };

  const handleRefreshClick = () => {
    refetch();
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  return (
    <>
      <Head>
        <title>Boekestyn Sales</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="mb-4 hidden border-b shadow md:block">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex">
                <nav className="ml-24 flex flex-row">
                  <Link
                    href={routes.boekestyns.list.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Boekestyn Item List
                  </Link>
                  <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                    Boekestyn Sales
                  </div>
                  <Link
                    href={routes.boekestyns.sticking.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Sticking
                  </Link>
                  <Link
                    href={routes.boekestyns.spacing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Spacing
                  </Link>
                  <Link
                    href={routes.boekestyns.upcs.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    UPCs
                  </Link>
                  <Link
                    href={routes.boekestyns.prep.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Order Prep
                  </Link>
                  <Link
                    href={routes.boekestyns.packing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Order Packing
                  </Link>
                  <Link
                    href={routes.boekestyns.admin.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Admin
                  </Link>
                </nav>
              </div>
            </div>
          </div>
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col rounded p-2">
                  <div className="grid w-full grid-cols-2 gap-2 rounded-sm text-xs md:grid-cols-8">
                    <div>
                      <label htmlFor="start-date">From Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="start-date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div>
                      <label htmlFor="end-date">To Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="end-date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div>
                      <label htmlFor="customer">Plant</label>
                      <select
                        id="plant"
                        value={filterPlant || ''}
                        onChange={handleFilterPlantChange}
                        className="w-full rounded-md border-gray-300 p-2 text-xs"
                      >
                        <option value="">Show All</option>
                        {plants.map((plant) => (
                          <option key={plant._id} value={plant._id}>
                            {plant.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    {customers.length > 1 && (
                      <div className="md:w-full">
                        <HeadlessUI.Listbox
                          value={productionCustomers}
                          onChange={handleProductionCustomerChange}
                          multiple
                        >
                          {({ open }) => (
                            <div>
                              <HeadlessUI.Listbox.Label>
                                Customer
                              </HeadlessUI.Listbox.Label>
                              <div className="relative w-full">
                                <HeadlessUI.Listbox.Button className="relative w-full cursor-default rounded-md bg-white py-2 pl-3 pr-10 text-left text-sm leading-6 shadow-sm ring-1 ring-inset ring-gray-300">
                                  <span className="block truncate text-xs">
                                    {productionCustomers.join(', ') ||
                                      'All Customers'}
                                  </span>
                                  <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                    <Icon
                                      icon="caret-down"
                                      className="h-5 w-5 text-gray-400"
                                    />
                                  </span>
                                </HeadlessUI.Listbox.Button>

                                <HeadlessUI.Transition
                                  show={open}
                                  as={Fragment}
                                  leave="transition ease-in duration-100"
                                  leaveFrom="opacity-100"
                                  leaveTo="opacity-0"
                                >
                                  <HeadlessUI.Listbox.Options className="absolute z-30 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                    {customers.map((customer) => (
                                      <HeadlessUI.Listbox.Option
                                        key={customer}
                                        className={({ active }) =>
                                          classNames(
                                            active
                                              ? 'bg-blue-600 text-white'
                                              : '',
                                            'relative cursor-default select-none py-2 pl-8 pr-4 text-sm'
                                          )
                                        }
                                        value={customer}
                                      >
                                        {({ selected, active }) => (
                                          <>
                                            <span
                                              className={classNames(
                                                selected
                                                  ? 'font-semibold'
                                                  : 'font-normal',
                                                'block truncate text-xs'
                                              )}
                                            >
                                              {customer}
                                            </span>

                                            {selected ? (
                                              <span
                                                className={classNames(
                                                  active
                                                    ? 'text-white'
                                                    : 'text-blue-600',
                                                  'absolute inset-y-0 left-0 flex items-center pl-1.5'
                                                )}
                                              >
                                                <Icon
                                                  icon="check-square"
                                                  className="h-5 w-5"
                                                />
                                              </span>
                                            ) : null}
                                          </>
                                        )}
                                      </HeadlessUI.Listbox.Option>
                                    ))}
                                  </HeadlessUI.Listbox.Options>
                                </HeadlessUI.Transition>
                              </div>
                            </div>
                          )}
                        </HeadlessUI.Listbox>
                      </div>
                    )}
                    <div className="col-start-1 flex items-start md:col-start-auto">
                      <div>
                        <label className="invisible block">&nbsp;</label>
                        <button
                          type="button"
                          className="btn-secondary px-2 py-1"
                          title="Reset Search Filters"
                          onClick={handleResetSearchClick}
                        >
                          <Icon
                            icon="magnifying-glass-arrows-rotate"
                            className="h-5 w-5"
                          />
                          <span className="md:hidden">
                            &nbsp;Reset Search Filters
                          </span>
                        </button>
                      </div>
                      <div className="ml-8 flex flex-row gap-4 pt-3">
                        <button
                          type="button"
                          onClick={handleRefreshClick}
                          className="btn-secondary text-blue-700"
                        >
                          <Icon icon="refresh" spin={isLoading} />
                          <span className="md:hidden">&nbsp;Refresh</span>
                        </button>
                        <button
                          type="button"
                          onClick={handleDownloadClick}
                          className="btn-secondary text-green-700"
                        >
                          <Icon icon="file-excel" />
                          <span className="md:hidden">&nbsp;Download</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full md:px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="flex h-full flex-col md:mt-8">
            <div className="h-full md:-mx-8 md:-my-2">
              <div className="inline-block min-w-full align-middle md:px-8 md:py-2">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300 text-xs">
                    <thead>
                      <tr className="sticky top-0 z-20">
                        <th className="bg-white p-2 text-left font-semibold text-gray-900">
                          Customer
                        </th>
                        {weeks.map(({ week, customer }) => (
                          <th
                            key={`${week.weekId}-${customer}`}
                            className="border-l border-r bg-white px-1 py-2 text-center font-semibold text-gray-900"
                          >
                            <div className="whitespace-nowrap">
                              {`Week ${week.week}`}
                            </div>
                            <div className="italic">{customer}</div>
                          </th>
                        ))}
                      </tr>
                      <tr className="text-base text-gray-900">
                        <th className="border bg-gray-100 p-2 text-left font-semibold">
                          Production
                        </th>
                        {weeks.map(({ week, customer }) => (
                          <ProductionItem
                            key={`${week.weekId}-${customer}`}
                            week={week}
                            customer={customer}
                          />
                        ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {salesCustomers.map((customer) => (
                        <CustomerItem key={customer} salesCustomer={customer} />
                      ))}
                      {!salesCustomers.length && (
                        <tr>
                          <td colSpan={weeks.length} className="p-2">
                            No Sales
                          </td>
                        </tr>
                      )}
                    </tbody>
                    <tbody className="bg-gray-100 p-2 text-center text-base font-semibold text-gray-900">
                      <tr className="border">
                        <th className="border p-2 text-left font-semibold">
                          Sales
                        </th>
                        {weeks.map(({ week, customer }) => (
                          <SalesItemSingle
                            key={`${week.weekId}-${customer}`}
                            week={week}
                            customer={customer}
                          />
                        ))}
                      </tr>
                    </tbody>
                    {hasMultipleProductItems && (
                      <>
                        {showMultipleCustomers && (
                          <tbody className="bg-white">
                            {bloomingAssortedSalesCustomers.map((customer) => (
                              <CustomerItem
                                key={customer}
                                salesCustomer={customer}
                                multiple
                              />
                            ))}
                          </tbody>
                        )}
                        <tbody className="bg-gray-100 p-2 text-center text-base font-semibold text-gray-900">
                          <tr className="border">
                            <th className="border p-2 text-left font-semibold">
                              <div className="flex w-full">
                                <div className="flex flex-grow">
                                  Sales (Blooming Assorted)
                                </div>
                                <div>
                                  {!!bloomingAssortedSalesCustomers.length && (
                                    <button
                                      type="button"
                                      className="btn-secondary p-2 text-xs focus:ring-0"
                                      onClick={
                                        handleToggleMultipleCustomersClick
                                      }
                                    >
                                      <Icon
                                        icon={
                                          showMultipleCustomers
                                            ? 'chevron-down'
                                            : 'chevron-up'
                                        }
                                      />
                                    </button>
                                  )}
                                </div>
                              </div>
                            </th>
                            {weeks.map(({ week, customer }) => (
                              <SalesItemMultiple
                                key={`${week.weekId}-${customer}`}
                                week={week}
                                customer={customer}
                              />
                            ))}
                          </tr>
                        </tbody>
                      </>
                    )}
                    <tbody className="bg-gray-100 p-2 text-center text-base font-semibold text-gray-900">
                      <tr className="border">
                        <th className="border p-2 text-left font-semibold">
                          Available
                        </th>
                        {weeks.map(({ week, customer }) => (
                          <AvailableItem
                            key={`${week.weekId}-${customer}`}
                            week={week}
                            customer={customer}
                          />
                        ))}
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
