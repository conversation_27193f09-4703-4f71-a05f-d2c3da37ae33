@layer components {
  a {
    @apply text-blue-500 hover:text-blue-900 hover:underline;
  }
  .btn {
    @apply items-center rounded-md border border-transparent px-4 py-2 text-sm font-medium shadow-sm hover:no-underline focus:outline-none;
  }

  .btn-new {
    @apply btn bg-green-600 text-white hover:bg-green-700 hover:text-white;
  }

  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700;
  }

  .btn-primary:disabled {
    @apply btn bg-blue-300 text-white;
  }

  .btn-secondary {
    @apply btn border-gray-300 bg-white text-gray-700 hover:bg-gray-50;
  }

  .btn-secondary:disabled {
    @apply btn text-gray-500 hover:bg-white;
  }

  .btn-delete {
    @apply btn border-red-600 bg-white text-red-700 hover:bg-red-100;
  }

  .btn-delete:disabled {
    @apply hover:bg-white;
  }

  input[type='search'] {
    @apply rounded-md border-gray-300 shadow-sm focus:border-blue-500;
  }
  input[type='date'] {
    max-width: 150px;
    @apply rounded-md border-gray-300 shadow-sm focus:border-blue-500;
  }
}
