import { useMemo, useRef, useState, Fragment } from 'react';
import { useDrop } from 'react-dnd';
import * as HeadlessUI from '@headlessui/react';
import {
  useAddStickingOrderToStickingScheduleMutation,
  useStickingOrdersQuery,
} from 'api/boekestyn-sticking-service';
import * as models from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { selectStartDate, selectEndDate } from './admin-slice';
import { selectSchedules, ScheduleStickingOrderType } from './sticking-slice';
import { StickingScheduleWorkOrder } from './sticking-schedule-work-order';

interface StickingScheduleProps {
  date: string;
  line: models.StickingLine;
}

export function StickingSchedule({ line, date }: StickingScheduleProps) {
  const [addOrderToSchedule] = useAddStickingOrderToStickingScheduleMutation(),
    [showDetailDialog, setShowDetailDialog] =
      useState<models.StickingOrder | null>(null),
    [crewSize, setCrewSize] = useState<number | null>(null),
    schedules = useAppSelector(selectSchedules),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    { refetch: refetchOrders } = useStickingOrdersQuery({
      startDate,
      endDate,
    }),
    schedule = useMemo(
      () =>
        schedules.find((s) => s.lineId === line.id && s.date === date) ?? {
          id: 0,
          lineId: line.id,
          date,
          workOrders: [],
        },
      [schedules, line.id, date]
    ),
    estimatedHours = useMemo(
      () =>
        schedule.workOrders.reduce((total, o) => total + o.estimatedHours, 0) ??
        0,
      [schedule]
    ),
    manHours = useMemo(
      () =>
        schedule.workOrders.reduce(
          (total, o) => total + o.estimatedHours * o.crewSize,
          0
        ) ?? 0,
      [schedule]
    ),
    [{ isOver }, drop] = useDrop<
      models.StickingOrder,
      void,
      { isOver: boolean }
    >(() => ({
      accept: ScheduleStickingOrderType,
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(order) {
        if (order.plant.defaultStickingCrewSize) {
          addOrderToSchedule({
            schedule,
            order,
          })
            .unwrap()
            .then(() => {
              refetchOrders();
            });
        } else {
          setShowDetailDialog(order);
        }
      },
    })),
    ref = useRef<HTMLDivElement>(null);

  const handleDetailCancel = () => {
    setShowDetailDialog(null);
  };

  const handleDetailConfirm = () => {
    if (crewSize && showDetailDialog) {
      const plant = {
          ...showDetailDialog.plant,
          defaultStickingCrewSize: crewSize,
        },
        order = { ...showDetailDialog, plant };
      addOrderToSchedule({
        schedule,
        order,
      });
    }
    setShowDetailDialog(null);
  };

  const handleDialogAfterEnter = () => {
    setCrewSize(1);
  };

  const handleCrewSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value, 10);
    setCrewSize(isNaN(value) ? null : value);
  };

  drop(ref);
  return (
    <div
      ref={ref}
      className={classNames(
        'm-2 rounded border p-2',
        isOver && 'border-green-600'
      )}
    >
      <h2 className="text-lg font-semibold">{line.name}</h2>
      <div className="flex-grow">
        {!!schedule.id && (
          <table className="min-w-full divide-y divide-gray-300 text-sm">
            <thead>
              <tr className="sticky top-0 z-10">
                <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                  Lot #
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                  Size / Plant
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Pots
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Hours
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-center">
                  Crew Size
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Man Hours
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  &nbsp;
                </th>
              </tr>
            </thead>
            <tbody>
              {schedule.workOrders.map((order) => (
                <StickingScheduleWorkOrder
                  key={order.id}
                  order={order}
                  scheduleId={schedule.id}
                />
              ))}
            </tbody>
            <tfoot>
              <tr>
                <th colSpan={3} className="p-2 text-right">
                  Total Hours:
                </th>
                <th className="p-2 text-right">
                  {formatNumber(estimatedHours, '0,0.0')}
                </th>
                <th className="p-2 text-right">&nbsp;</th>
                <th className="p-2 text-right">
                  {formatNumber(manHours, '0,0.0')}
                </th>
                <th className="p-2 text-right">&nbsp;</th>
              </tr>
            </tfoot>
          </table>
        )}
        {!schedule.id && (
          <div className="flex h-24 items-center justify-center text-gray-500">
            <span className="text-sm italic">Drag to schedule orders.</span>
          </div>
        )}
      </div>
      <HeadlessUI.Transition.Root
        show={!!showDetailDialog}
        as={Fragment}
        afterEnter={handleDialogAfterEnter}
      >
        <HeadlessUI.Dialog
          as="div"
          className="relative z-30"
          onClose={() => setShowDetailDialog(null)}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-0 text-center">
              <HeadlessUI.Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-0 scale-95"
                enterTo="opacity-100 translate-y-0 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 scale-100"
                leaveTo="opacity-0 translate-y-0 scale-95"
              >
                <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-md transform overflow-hidden rounded-lg bg-white p-4 text-left shadow-xl transition-all">
                  <div className="rounded border p-8">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {showDetailDialog?.orderNumber}
                    </h3>
                    <div className="mt-2">
                      <label className="block text-sm font-medium text-gray-500">
                        Crew Size
                      </label>
                      <input
                        type="number"
                        value={crewSize ?? ''}
                        onChange={handleCrewSizeChange}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                      <p className="mt-2 text-sm text-gray-500">
                        Set the crew size for this order.
                      </p>
                    </div>
                  </div>
                  <div className="mt-4 text-right">
                    <button
                      type="button"
                      className="btn-secondary"
                      onClick={handleDetailCancel}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="btn-primary ml-2"
                      onClick={handleDetailConfirm}
                      disabled={!crewSize}
                    >
                      Save &nbsp;
                      <Icon icon="save" />
                    </button>
                  </div>
                </HeadlessUI.Dialog.Panel>
              </HeadlessUI.Transition.Child>
            </div>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>
    </div>
  );
}
