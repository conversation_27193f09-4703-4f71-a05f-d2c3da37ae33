import {
  PayloadAction,
  createAction,
  createSlice,
  createSelector,
  AsyncThunk,
  createAsyncThunk,
} from '@reduxjs/toolkit';
import { DateTime } from 'luxon';
import {
  futureOrdersListApi,
  futureOrdersApi,
  FutureOrderDetailResponse,
} from 'api/future-orders-service';
import {
  SpireCustomerDetailResponse,
  spireApi,
  spireService,
} from 'api/spire-service';
import * as futureOrders from 'api/models/future-orders';
import * as spire from 'api/models/spire';
import { RootState } from '@/services/store';
import { contains, equals, startsWith } from '@/utils/equals';
import { ProblemDetails } from '@/utils/problem-details';
import { sortBy } from '@/utils/sort';
import { formatNumber } from '@/utils/format';

export const blankShipToFilter = 'No Ship To';
export const blankCustomerFilter = 'No Customer';

export const getDetail: AsyncThunk<
  FutureOrderDetailResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'split-order-getDetail',
  async (id, { rejectWithValue }) => {
    try {
      return await futureOrdersApi.detail(id);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const getCustomerDetail: AsyncThunk<
  SpireCustomerDetailResponse | null,
  number | null,
  { state: RootState }
> = createAsyncThunk(
  'splitOrder-setCustomerDetail',
  async (id, { rejectWithValue }) => {
    try {
      return id ? await spireService.customerDetail(id) : null;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface Filters {
  customer: string | null;
  shipTo: string | null;
}

export interface SplitOrderItem {
  id: number;
  sortOrder: number;
  spirePartNumber: string | null;
  description: string | null;
  orderQuantity: number;
  selected: boolean;
  splitQuantity: number;
}

export type DestinationType = 'new' | 'existing';

interface SplitOrderState {
  destinationType: DestinationType;
  step: number;
  search: string;
  sort: keyof futureOrders.FutureOrderSummaryItem;
  sortDescending: boolean;
  filter: Filters;
  futureOrders: futureOrders.FutureOrderSummaryItem[];
  requiredDate: string | null;
  arrivalDate: string | null;
  seasonName: string | null;
  salesperson: spire.Salesperson | null;
  shipTo: spire.CustomerShipTo | null;
  customer: spire.Customer | null;
  customerDetail: spire.CustomerDetail | null;
  shipVia: spire.ShippingMethod | null;
  boxCode: string | null;
  requiresLabels: boolean;
  customerPurchaseOrderNumber: string | null;
  spireNotes: string | null;
  growerItemNotes: string | null;
  comments: futureOrders.FutureOrderDetailComment[];
  internalComments: string | null;
  futureOrderDetail: futureOrders.FutureOrderDetail | null;
  salespeople: spire.Salesperson[];
  isLoading: boolean;
  error: ProblemDetails | null;
  items: SplitOrderItem[];
}

const initialState: SplitOrderState = {
  destinationType: 'new',
  step: 1,
  search: '',
  sort: 'date',
  sortDescending: false,
  filter: { customer: null, shipTo: null },
  futureOrders: [],
  requiredDate: null,
  arrivalDate: null,
  seasonName: null,
  customer: null,
  customerDetail: null,
  shipTo: null,
  salesperson: null,
  shipVia: null,
  boxCode: null,
  requiresLabels: false,
  customerPurchaseOrderNumber: null,
  spireNotes: null,
  growerItemNotes: null,
  comments: [],
  internalComments: null,
  futureOrderDetail: null,
  salespeople: [],
  isLoading: false,
  error: null,
  items: [],
};

const getDetailFulfilled = createAction<FutureOrderDetailResponse>(
    getDetail.fulfilled.type
  ),
  getDetailRejected = createAction<ProblemDetails>(getDetail.rejected.type),
  getCustomerDetailFulfilled = createAction<SpireCustomerDetailResponse | null>(
    getCustomerDetail.fulfilled.type
  );

interface SetItemArgs<T> {
  itemId: number;
  value: T;
}

const splitOrderSlice = createSlice({
  name: 'splitOrder',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
    setError(state, { payload }: PayloadAction<ProblemDetails>) {
      state.error = payload;
    },
    setDestinationType(state, { payload }: PayloadAction<DestinationType>) {
      state.destinationType = payload;
      state.futureOrderDetail = null;
    },
    setStep(state, { payload }: PayloadAction<number>) {
      state.step = payload;
    },
    setSearch(state, { payload }: PayloadAction<string>) {
      state.search = payload;
    },
    setSort(
      state,
      {
        payload,
      }: PayloadAction<{
        sort: keyof futureOrders.FutureOrderSummaryItem;
        sortDescending: boolean;
      }>
    ) {
      state.sort = payload.sort;
      state.sortDescending = payload.sortDescending;
    },
    setCustomerFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, customer: payload };
      state.filter = filter;
    },
    setShipToFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, shipTo: payload };
      state.filter = filter;
    },
    setItems(
      state,
      { payload }: PayloadAction<futureOrders.FutureOrderDetailItem[]>
    ) {
      state.items = payload.map(
        ({ id, sortOrder, spirePartNumber, description, orderQuantity }) => ({
          id,
          sortOrder,
          spirePartNumber,
          description,
          orderQuantity,
          selected: false,
          splitQuantity: 0,
        })
      );
    },
    setItemSelected(state, { payload }: PayloadAction<SetItemArgs<boolean>>) {
      const items = state.items.map((i) => ({ ...i })),
        item = items.find((i) => i.id === payload.itemId);

      if (item) {
        item.selected = payload.value;
      }

      state.items = items;
    },
    setItemSplitQuantity(
      state,
      { payload }: PayloadAction<SetItemArgs<number>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        item = items.find((i) => i.id === payload.itemId);

      if (item) {
        item.splitQuantity = payload.value;
      }

      state.items = items;
    },
    setCustomer(state, { payload }: PayloadAction<spire.Customer | null>) {
      state.customer = payload;
    },
    setShipTo(state, { payload }: PayloadAction<spire.CustomerShipTo | null>) {
      state.shipTo = payload;

      const salesperson =
          state.salespeople.find(
            (s) => s.code === payload?.salesperson?.code
          ) || null,
        boxCode = payload?.boxCode || null,
        labels = payload?.labels || null;

      state.salesperson = salesperson;
      state.boxCode = boxCode;
      state.requiresLabels = equals(labels, futureOrders.SpecialLabels);
    },
    setSalesperson(
      state,
      { payload }: PayloadAction<spire.Salesperson | null>
    ) {
      state.salesperson = payload;
    },
    setRequiredDate(state, { payload }: PayloadAction<string | null>) {
      state.requiredDate = payload;
    },
    setArrivalDate(state, { payload }: PayloadAction<string | null>) {
      state.arrivalDate = payload;
    },
    setSeasonName(state, { payload }: PayloadAction<string | null>) {
      state.seasonName = payload;
    },
    setShipVia(state, { payload }: PayloadAction<spire.ShippingMethod | null>) {
      state.shipVia = payload;
    },
    setBoxCode(state, { payload }: PayloadAction<string | null>) {
      state.boxCode = payload;
    },
    setRequiresLabels(state, { payload }: PayloadAction<boolean>) {
      state.requiresLabels = payload;
    },
    setCustomerPurchaseOrderNumber(
      state,
      { payload }: PayloadAction<string | null>
    ) {
      state.customerPurchaseOrderNumber = payload;
    },
    setComments(
      state,
      { payload }: PayloadAction<futureOrders.FutureOrderDetailComment[]>
    ) {
      state.comments = payload;
    },
    setInternalComments(state, { payload }: PayloadAction<string | null>) {
      state.internalComments = payload;
    },
    setGrowerItemNotes(state, { payload }: PayloadAction<string | null>) {
      state.growerItemNotes = payload;
    },
    setSpireNotes(state, { payload }: PayloadAction<string | null>) {
      state.spireNotes = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getDetailFulfilled, (state, { payload }) => {
        state.futureOrderDetail = payload.futureOrder;
        const {
          customerId,
          customerName,
          shipToId,
          shipToName,
          salespersonId,
          salespersonName,
          shipViaId,
          shipViaName,
          requiredDate,
          arrivalDate,
          seasonName,
          boxCode,
          requiresLabels,
          freightPerCase,
          customerPurchaseOrderNumber,
          spireNotes,
          growerItemNotes,
          comments,
        } = payload.futureOrder;

        const customer = customerId
            ? { id: customerId, name: customerName || '', defaultShipTo: null }
            : null,
          salesperson = salespersonId
            ? { id: salespersonId, name: salespersonName || '', code: '' }
            : null,
          shipTo = shipToId
            ? {
                id: shipToId,
                name: shipToName || '',
                shipId: shipToName || '',
                boxCode,
                labels: requiresLabels ? futureOrders.SpecialLabels : null,
                salesperson,
                customerInfo: null,
                defaultFreightPerCase: freightPerCase,
                priceLevel: null,
              }
            : null,
          shipVia = shipViaId
            ? { id: shipViaId, description: shipViaName || '', code: '' }
            : null;
        state.customer = customer;
        state.shipTo = shipTo;
        state.salesperson = salesperson;
        state.shipVia = shipVia;
        state.requiredDate = requiredDate
          ? DateTime.fromISO(requiredDate).toFormat('yyyy-MM-dd')
          : null;
        state.arrivalDate = arrivalDate
          ? DateTime.fromISO(arrivalDate).toFormat('yyyy-MM-dd')
          : null;
        state.seasonName = seasonName;
        state.boxCode = boxCode;
        state.requiresLabels = requiresLabels;
        state.customerPurchaseOrderNumber = customerPurchaseOrderNumber;
        state.spireNotes = spireNotes;
        state.growerItemNotes = growerItemNotes;
        state.comments = comments;
      })
      .addCase(getDetailRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(getCustomerDetailFulfilled, (state, { payload }) => {
        if (payload) {
          const { customer: detail, customerItemCodes, potCovers } = payload,
            customer = {
              id: detail.id,
              customerNo: detail.customerNo,
              name: detail.name,
              defaultShipTo: detail.defaultShipTo,
            },
            shipTo =
              detail.shippingAddresses.find((a) => {
                if (state.shipTo?.id) {
                  return a.id === state.shipTo.id;
                } else {
                  return a.shipId === detail.defaultShipTo;
                }
              }) || null;

          state.customerDetail = detail;
          state.customer = customer;
          state.shipTo = shipTo;
          if (shipTo) {
            state.salesperson =
              state.salespeople.find(
                (s) => s.code === shipTo.salesperson?.code
              ) || null;
            state.boxCode = shipTo.boxCode || null;
          }
        } else {
          state.customerDetail = null;
          state.customer = null;
          state.shipTo = null;
          state.salesperson = null;
          state.boxCode = null;
        }
      })
      .addMatcher(
        futureOrdersListApi.endpoints.itemSummary.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.itemSummary.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.futureOrders = payload.items;
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.itemSummary.matchRejected,
        (state, { payload }) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      )
      .addMatcher(
        spireApi.endpoints.salespeople.matchFulfilled,
        (state, { payload }) => {
          state.salespeople = payload;
        }
      ),
});

export const {
  setItems,
  setItemSelected,
  setItemSplitQuantity,
  clearState,
  clearError,
  setError,
  setDestinationType,
  setStep,
  setSearch,
  setSort,
  setCustomerFilter,
  setShipToFilter,
  setCustomer,
  setShipTo,
  setSalesperson,
  setRequiredDate,
  setArrivalDate,
  setSeasonName,
  setShipVia,
  setBoxCode,
  setRequiresLabels,
  setCustomerPurchaseOrderNumber,
  setComments,
  setInternalComments,
  setGrowerItemNotes,
  setSpireNotes,
} = splitOrderSlice.actions;

export const selectError = ({ splitOrder }: RootState) => splitOrder.error;
export const selectIsLoading = ({ splitOrder }: RootState) =>
  splitOrder.isLoading;
export const selectDestinationType = ({ splitOrder }: RootState) =>
  splitOrder.destinationType;
export const selectStep = ({ splitOrder }: RootState) => splitOrder.step;
export const selectSearch = ({ splitOrder }: RootState) => splitOrder.search;
export const selectSort = ({ splitOrder }: RootState) => splitOrder.sort;
export const selectSortDescending = ({ splitOrder }: RootState) =>
  splitOrder.sortDescending;
export const selectFilter = ({ splitOrder }: RootState) => splitOrder.filter;
export const selectAllFutureOrders = ({ splitOrder }: RootState) =>
  splitOrder.futureOrders;
export const selectCustomer = ({ splitOrder }: RootState) =>
  splitOrder.customer;
export const selectCustomerDetail = ({ splitOrder }: RootState) =>
  splitOrder.customerDetail;
export const selectShipTo = ({ splitOrder }: RootState) => splitOrder.shipTo;
export const selectSalesperson = ({ splitOrder }: RootState) =>
  splitOrder.salesperson;
export const selectShipVia = ({ splitOrder }: RootState) => splitOrder.shipVia;
export const selectRequiredDate = ({ splitOrder }: RootState) =>
  splitOrder.requiredDate;
export const selectArrivalDate = ({ splitOrder }: RootState) =>
  splitOrder.arrivalDate;
export const selectSeasonName = ({ splitOrder }: RootState) =>
  splitOrder.seasonName;
export const selectBoxCode = ({ splitOrder }: RootState) => splitOrder.boxCode;
export const selectRequiresLabels = ({ splitOrder }: RootState) =>
  splitOrder.requiresLabels;
export const selectCustomerPurchaseOrderNumber = ({ splitOrder }: RootState) =>
  splitOrder.customerPurchaseOrderNumber;
export const selectGrowerItemNotes = ({ splitOrder }: RootState) =>
  splitOrder.growerItemNotes;
export const selectSpireNotes = ({ splitOrder }: RootState) =>
  splitOrder.spireNotes;
export const selectComments = ({ splitOrder }: RootState) =>
  splitOrder.comments;
export const selectInternalComments = ({ splitOrder }: RootState) =>
  splitOrder.internalComments;
export const selectFutureOrderDetail = ({ splitOrder }: RootState) =>
  splitOrder.futureOrderDetail;
export const selectSalespeople = ({ splitOrder }: RootState) =>
  splitOrder.salespeople;
export const selectItems = ({ splitOrder }: RootState) => splitOrder.items;

export const selectFutureOrderCustomers = createSelector(
  selectAllFutureOrders,
  (futureOrders) => {
    const customers = futureOrders
      .reduce(
        (memo, p) =>
          p.customer && memo.indexOf(p.customer) === -1
            ? memo.concat([p.customer])
            : memo,
        [] as string[]
      )
      .sort();

    if (futureOrders.some((o) => !o.customer)) {
      customers.unshift(blankCustomerFilter);
    }

    return customers;
  }
);
export const selectFutureOrderShipTos = createSelector(
  selectAllFutureOrders,
  selectFilter,
  (futureOrders, { customer }) => {
    const shipTos = futureOrders
      // if there's a customer filter, only show those ones
      .filter(
        (o) =>
          !customer ||
          equals(customer, o.customer) ||
          (customer === blankCustomerFilter && !o.customer)
      )
      .reduce(
        (memo, p) =>
          p.shipTo && memo.indexOf(p.shipTo) === -1
            ? memo.concat([p.shipTo])
            : memo,
        [] as string[]
      )
      .sort();

    if (futureOrders.some((o) => !o.shipTo)) {
      shipTos.unshift(blankShipToFilter);
    }

    return shipTos;
  }
);

export const selectFutureOrders = createSelector(
  selectAllFutureOrders,
  selectSearch,
  selectSort,
  selectSortDescending,
  selectFilter,
  (futureOrders, search, sortField, sortDescending, filter) => {
    const sort = sortBy(sortField, sortDescending ? 'descending' : ''),
      list = futureOrders
        .filter(
          (o) =>
            (!search ||
              contains(formatNumber(o.futureOrderId, '00000'), search) ||
              startsWith(o.customer, search) ||
              startsWith(o.shipTo, search) ||
              startsWith(o.salesperson, search) ||
              contains(o.spirePartNumber, search) ||
              contains(o.customerItemCode, search)) &&
            (!filter.customer ||
              equals(filter.customer, o.customer) ||
              (filter.customer === blankCustomerFilter && !o.customer)) &&
            (!filter.shipTo ||
              equals(filter.shipTo, o.shipTo) ||
              (filter.shipTo === blankShipToFilter && !o.shipTo))
        )
        .reduce((memo, o) => {
          if (!memo.some((m) => m.futureOrderId === o.futureOrderId)) {
            memo.push(o);
          }
          return memo;
        }, [] as futureOrders.FutureOrderSummaryItem[])
        .sort(sort);

    return list;
  }
);

export default splitOrderSlice.reducer;
